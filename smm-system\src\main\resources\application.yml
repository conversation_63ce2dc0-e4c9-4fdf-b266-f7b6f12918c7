server:
  port: 8095
  servlet:
    context-path: /api




logging:
  file:
    name: /var/log/smm-system.log
  logback:
    rolling policy:
      max-file-size: 10MB
      max-history: 7
  level:
    root: info
    tndung.vnfb.smm.config.TenantAuthenticationFilter: debug
    tndung.vnfb.smm.config.AuthenticationFilter: debug
    tndung.vnfb.smm.config.TenantContext: debug
  pattern:
    console: '%d{HH:mm:ss} [%thread] %highlight(%-5level) %logger{30} - %msg%n'
    file: '%d{MM-dd HH:mm:ss} [%thread] %-5level %logger{30} - %msg%n'


spring:
  jackson:
    property-naming-strategy: SNAKE_CASE
  profiles:
    active: @spring.profiles.active@
  messages:
    basename: messages
#  autoconfigure:
#    exclude: org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration

config:
  thread:
    request:
      core-pool-size: 2
      max-pool-size: 4
      thread-name-prefix: vnfb-async
    batch:
      core-pool-size: 10
      max-pool-size: 30
      thread-name-prefix: bot-async

jwt:
  header:
    authorization: authorization
    client-id: x-client-id
    refresh-token: x-refresh-token
    prefix: Bearer
  access-token:
    validity: 172800
  refresh-token:
    validity: 604800
  auth:
    token-name: token
  private-key: nio-8096-exec-1
  public-key: nio-8096-exec-8
  authorities:
    key: roles


currency:
  api.url: https://api.exchangerate-api.com/v4/latest/
  api.base-currency: USD

# File upload configuration
file:
  upload:
    dir: /app/uploads
    max-size: 10MB
  cdn:
    url: http://localhost/cdn
